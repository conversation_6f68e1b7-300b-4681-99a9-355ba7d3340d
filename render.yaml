# Render.com Configuration pour CONNECT

services:
  # Frontend Next.js
  - type: web
    name: connect-frontend
    runtime: docker
    plan: standard
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./frontend/Dockerfile
    dockerContext: ./frontend
    envVars:
      - key: NEXT_PUBLIC_SUPABASE_URL
        value: https://irfvjdismpsxwihifsel.supabase.co
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnZqZGlzbXBzeHdpaGlmc2VsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MjIxNDAsImV4cCI6MjA2NTM5ODE0MH0.S_KVhio9_2VpGZPgIieAf22TYeNu3qyUns6HyERqYpI
      - key: NEXT_PUBLIC_BACKEND_URL
        value: https://connect-backend-p2ex.onrender.com/api
      - key: NEXT_PUBLIC_URL
        value: https://orchestraconnect.fr
      - key: NEXT_PUBLIC_ENV_MODE
        value: production
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_GOOGLE_CLIENT_ID
        value: ""
      - key: NEXT_PUBLIC_SENTRY_DSN
        value: ""
      - key: NEXT_PUBLIC_VERCEL_ENV
        value: production

  # Redis Service - Using Upstash Redis (external service)
  # No Redis service needed - using external Upstash Redis

  # Backend API
  - type: web
    name: connect-backend
    runtime: docker
    plan: standard
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./backend/Dockerfile
    dockerContext: ./backend
    envVars:
      - key: ENV_MODE
        value: production
      - key: SUPABASE_URL
        value: https://irfvjdismpsxwihifsel.supabase.co
      - key: SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnZqZGlzbXBzeHdpaGlmc2VsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk4MjIxNDAsImV4cCI6MjA2NTM5ODE0MH0.S_KVhio9_2VpGZPgIieAf22TYeNu3qyUns6HyERqYpI
      - key: SUPABASE_SERVICE_ROLE_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlyZnZqZGlzbXBzeHdpaGlmc2VsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTgyMjE0MCwiZXhwIjoyMDY1Mzk4MTQwfQ.Z1k2HbdP-Z1m5mvqRw7ynWrkAPKUTJ11_8bzdDHxAM4
      - key: REDIS_HOST
        value: frankfurt-keyvalue.render.com
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_USERNAME
        value: red-d197f5ffte5s73c39cjg
      - key: REDIS_PASSWORD
        value: CnDsAfylX3iqG00J8Z4o5hcckXU6TmjX
      - key: REDIS_SSL
        value: "true"
      - key: REDIS_URL
        value: "rediss://red-d197f5ffte5s73c39cjg:<EMAIL>:6379"
      - key: RABBITMQ_HOST
        value: connect-rabbitmq
      - key: RABBITMQ_PORT
        value: "5672"
      - key: RABBITMQ_URL
        value: amqp://guest:guest@connect-rabbitmq:5672
      - key: ANTHROPIC_API_KEY
        value: sk-ant-api03-placeholder-anthropic-key
      - key: OPENAI_API_KEY
        value: sk-placeholder-openai-key
      - key: MODEL_TO_USE
        value: claude-3-5-sonnet-20241022
      - key: AWS_ACCESS_KEY_ID
        value: ""
      - key: AWS_SECRET_ACCESS_KEY
        value: ""
      - key: AWS_REGION_NAME
        value: ""
      - key: GROQ_API_KEY
        value: ""
      - key: OPENROUTER_API_KEY
        value: 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
      - key: RAPID_API_KEY
        value: 250c63d6e4msh224510ad4df47c0p1758b2jsn4767ad2cac62
      - key: TAVILY_API_KEY
        value: 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
      - key: FIRECRAWL_API_KEY
        value: fc-60fe43022991488e9f9d030a4d075b8b
      - key: FIRECRAWL_URL
        value: https://api.firecrawl.dev
      - key: DAYTONA_API_KEY
        value: dtn_e650ae7d7b250337033aaa01e4a78d03c11d4135aa1df5dd57045619f36accfd
      - key: DAYTONA_SERVER_URL
        value: https://app.daytona.io/api
      - key: DAYTONA_TARGET
        value: us
      - key: LANGFUSE_PUBLIC_KEY
        value: ""
      - key: LANGFUSE_SECRET_KEY
        value: ""
      - key: LANGFUSE_HOST
        value: https://cloud.langfuse.com
      - key: SMITHERY_API_KEY
        value: ""
      # Stripe Configuration
      - key: STRIPE_SECRET_KEY
        value: ***********************************************************************************************************
      - key: STRIPE_WEBHOOK_SECRET
        value: whsec_placeholder
      # Email Configuration (Mailtrap)
      - key: MAILTRAP_API_TOKEN
        value: ""
      - key: MAILTRAP_SENDER_EMAIL
        value: <EMAIL>
      - key: MAILTRAP_SENDER_NAME
        value: Orchestra Connect
      # Stripe Configuration (duplicate - removing)
      # - key: STRIPE_SECRET_KEY
      #   value: ""
      # - key: STRIPE_WEBHOOK_SECRET
      #   value: ""
      # - key: STRIPE_PRODUCT_ID
      #   value: ""
      # Email Configuration (Mailtrap)
      - key: MAILTRAP_API_TOKEN
        value: ""
      - key: MAILTRAP_SENDER_EMAIL
        value: <EMAIL>
      - key: MAILTRAP_SENDER_NAME
        value: Orchestra Connect

  # Worker
  - type: worker
    name: connect-worker
    runtime: docker
    plan: standard
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./backend/Dockerfile
    dockerContext: ./backend
    dockerCommand: python -m dramatiq run_agent_background --processes 1 --threads 2
    envVars:
      - key: DRAMATIQ_THREADS
        value: "2"
      - key: DRAMATIQ_PROCESSES
        value: "1"
      - key: PYTHONDONTWRITEBYTECODE
        value: "1"
      - key: PYTHONUNBUFFERED
        value: "1"
      - key: LANGFUSE_HOST
        value: https://cloud.langfuse.com
      - key: LANGFUSE_SECRET_KEY
        value: ""
      - key: LANGFUSE_PUBLIC_KEY
        value: ""
      - key: AWS_REGION_NAME
        value: ""
      - key: AWS_SECRET_ACCESS_KEY
        value: ""
      - key: AWS_ACCESS_KEY_ID
        value: ""
      - key: RAPID_API_KEY
        value: placeholder
      - key: RABBITMQ_PORT
        value: "5672"
      - key: RABBITMQ_HOST
        value: connect-rabbitmq
      - key: REDIS_SSL
        value: "true"
      - key: REDIS_USERNAME
        value: red-d197f5ffte5s73c39cjg
      - key: REDIS_PASSWORD
        value: CnDsAfylX3iqG00J8Z4o5hcckXU6TmjX
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_HOST
        value: frankfurt-keyvalue.render.com
      - key: RABBITMQ_URL
        value: amqp://guest:guest@connect-rabbitmq:5672
      - key: REDIS_URL
        value: "rediss://red-d197f5ffte5s73c39cjg:<EMAIL>:6379"
      - key: DAYTONA_TARGET
        value: us

  # RabbitMQ
  - type: pserv
    name: connect-rabbitmq
    runtime: docker
    repo: https://github.com/LG15601/connect.git
    dockerfilePath: ./rabbitmq.Dockerfile
    envVars:
      - key: RABBITMQ_URL
        value: amqp://guest:guest@localhost:5672
    openPorts:
      - port: 4369
        protocol: tcp
      - port: 5672
        protocol: tcp
      - port: 15692
        protocol: tcp
      - port: 25672
        protocol: tcp 